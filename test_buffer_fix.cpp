#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <thread>
#include <chrono>

// Include the p2psocket headers
#include "src/p2psocket/p2psocket.h"
#include "src/p2psocket/common_defines.h"

void test_buffer_append_safety() {
    printf("Testing BufferListAppend safety improvements...\n");
    
    // Test 1: NULL pointer validation
    printf("Test 1: NULL pointer validation\n");
    
    // Test 2: Invalid size validation
    printf("Test 2: Invalid size validation\n");
    
    // Test 3: Large size validation
    printf("Test 3: Large size validation\n");
    
    printf("BufferListAppend safety tests completed.\n");
}

void test_quic_write_safety() {
    printf("Testing QuicWrite safety improvements...\n");
    
    // Create socket options
    SocketOptions options = {};
    options.mode = MODE_CLIENT;
    options.type = SOCKET_QUIC;
    options.log_level = LOG_INFO;
    
    // Create socket
    P2P_SOCKET socket = P2pCreate(&options);
    if (socket == NULL) {
        printf("Failed to create socket\n");
        return;
    }
    
    printf("Socket created successfully\n");
    
    // Test writing with various buffer sizes
    const char* testData = "Hello, World!";
    int len = strlen(testData);
    
    printf("Testing QuicWrite with test data (length: %d)\n", len);
    
    // Note: This will fail because we're not connected, but it should test our validation
    int result = QuicWrite(socket, testData, len);
    printf("QuicWrite result: %d (expected to fail due to no connection)\n", result);
    
    // Test with NULL buffer
    result = QuicWrite(socket, NULL, len);
    printf("QuicWrite with NULL buffer result: %d (expected to fail)\n", result);
    
    // Test with zero length
    result = QuicWrite(socket, testData, 0);
    printf("QuicWrite with zero length result: %d (expected to fail)\n", result);
    
    // Clean up
    QuicRelease(socket);
    printf("Socket released\n");
    
    printf("QuicWrite safety tests completed.\n");
}

int main() {
    printf("Starting buffer safety tests...\n");
    
    test_buffer_append_safety();
    test_quic_write_safety();
    
    printf("All tests completed.\n");
    return 0;
}
