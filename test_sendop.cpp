// Test program to verify the send operation changes
#include <iostream>
#include <cstring>

// Mock structures for testing
typedef struct QUIC_BUFFER {
    uint32_t Length;
    uint8_t* Buffer;
} QUIC_BUFFER;

typedef struct SEND_OPERATION {
    uint64_t operationId;              // Operation unique ID
    QUIC_BUFFER* buffers;              // Dynamically allocated send buffer array
    int bufferCount;                   // Number of buffers
    uint32_t totalBytes;               // Total bytes in this operation
    void* sourceBuffers;               // Source data buffer list (for cleanup)
    int sourceBufferCount;             // Number of source buffers
    int isActive;                      // Whether this operation is active (using int instead of BOOLEAN)
} SEND_OPERATION;

// Test function to verify send operation initialization
void test_send_operation_init() {
    SEND_OPERATION sendOp;
    
    // Initialize like in the real code
    sendOp.isActive = 0; // FALSE
    sendOp.buffers = nullptr;
    sendOp.operationId = 0;
    sendOp.bufferCount = 0;
    sendOp.totalBytes = 0;
    sendOp.sourceBuffers = nullptr;
    sendOp.sourceBufferCount = 0;
    
    std::cout << "Send operation initialized successfully" << std::endl;
    std::cout << "isActive: " << sendOp.isActive << std::endl;
    std::cout << "operationId: " << sendOp.operationId << std::endl;
    std::cout << "bufferCount: " << sendOp.bufferCount << std::endl;
}

// Test function to verify dynamic buffer allocation
void test_dynamic_buffer_allocation() {
    SEND_OPERATION sendOp;
    int bufferCount = 5;
    
    // Simulate dynamic allocation
    sendOp.buffers = (QUIC_BUFFER*)malloc(bufferCount * sizeof(QUIC_BUFFER));
    if (sendOp.buffers == nullptr) {
        std::cout << "Failed to allocate buffers" << std::endl;
        return;
    }
    
    sendOp.bufferCount = bufferCount;
    sendOp.isActive = 1; // TRUE
    sendOp.operationId = 12345;
    
    // Fill buffers with test data
    for (int i = 0; i < bufferCount; i++) {
        sendOp.buffers[i].Length = 100 + i;
        sendOp.buffers[i].Buffer = (uint8_t*)malloc(sendOp.buffers[i].Length);
        if (sendOp.buffers[i].Buffer) {
            memset(sendOp.buffers[i].Buffer, 'A' + i, sendOp.buffers[i].Length);
        }
    }
    
    std::cout << "Dynamic buffer allocation test passed" << std::endl;
    std::cout << "Operation ID: " << sendOp.operationId << std::endl;
    std::cout << "Buffer count: " << sendOp.bufferCount << std::endl;
    std::cout << "Is active: " << sendOp.isActive << std::endl;
    
    // Cleanup
    for (int i = 0; i < bufferCount; i++) {
        if (sendOp.buffers[i].Buffer) {
            free(sendOp.buffers[i].Buffer);
        }
    }
    free(sendOp.buffers);
    sendOp.buffers = nullptr;
    sendOp.isActive = 0;
    
    std::cout << "Cleanup completed successfully" << std::endl;
}

// Test function to verify operation ID tracking
void test_operation_id_tracking() {
    uint64_t nextOpId = 0;
    SEND_OPERATION sendOp1, sendOp2, sendOp3;
    
    // Simulate creating multiple operations
    sendOp1.operationId = ++nextOpId;
    sendOp1.isActive = 1;
    
    sendOp2.operationId = ++nextOpId;
    sendOp2.isActive = 1;
    
    sendOp3.operationId = ++nextOpId;
    sendOp3.isActive = 1;
    
    std::cout << "Operation ID tracking test:" << std::endl;
    std::cout << "Op1 ID: " << sendOp1.operationId << std::endl;
    std::cout << "Op2 ID: " << sendOp2.operationId << std::endl;
    std::cout << "Op3 ID: " << sendOp3.operationId << std::endl;
    std::cout << "Next ID: " << nextOpId << std::endl;
    
    // Verify IDs are unique and sequential
    if (sendOp1.operationId == 1 && sendOp2.operationId == 2 && sendOp3.operationId == 3) {
        std::cout << "Operation ID tracking test passed" << std::endl;
    } else {
        std::cout << "Operation ID tracking test failed" << std::endl;
    }
}

int main() {
    std::cout << "=== Send Operation Test Suite ===" << std::endl;
    
    test_send_operation_init();
    std::cout << std::endl;
    
    test_dynamic_buffer_allocation();
    std::cout << std::endl;
    
    test_operation_id_tracking();
    std::cout << std::endl;
    
    std::cout << "All tests completed!" << std::endl;
    return 0;
}
